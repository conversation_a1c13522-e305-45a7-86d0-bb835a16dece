import React, { useState, useEffect, useRef } from 'react';
import {
  FaWallet,
  FaMoneyBillWave,
  FaPlus,
  FaArrowUp,
  FaArrowDown,
  FaFilter,
  FaChartLine
} from 'react-icons/fa';
import Card from '../components/Card';
import Button from '../components/Button';
import Modal from '../components/Modal';
import FormattedCurrency from '../components/FormattedCurrency';
import { useApp } from '../context/AppContext';
import './Cashbox.css';

const Cashbox = () => {
  // State variables
  const [cashbox, setCashbox] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [transactions, setTransactions] = useState([]);

  // Modal states
  const [showInitialBalanceModal, setShowInitialBalanceModal] = useState(false);
  const [showEditInitialBalanceModal, setShowEditInitialBalanceModal] = useState(false);
  const [showAddTransactionModal, setShowAddTransactionModal] = useState(false);
  const [initialBalance, setInitialBalance] = useState(0);
  const [transactionForm, setTransactionForm] = useState({
    type: 'income',
    amount: '',
    source: '',
    notes: ''
  });

  // Filter states
  const [filters, setFilters] = useState({
    type: '',
    source: '',
    startDate: '',
    endDate: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  // App context
  const { showNotification, updateCashboxInitialBalance } = useApp();

  // Refs
  const formRef = useRef(null);
  const isMounted = useRef(true);

  // Component mount/unmount lifecycle
  useEffect(() => {
    isMounted.current = true;

    // Load cashbox data on component mount
    loadCashbox();

    // Cleanup on unmount
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Enable form fields after modal opens
  const enableFormFields = () => {
    if (formRef.current && isMounted.current) {
      const inputs = formRef.current.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.disabled = false;
      });
    }
  };

  // Load cashbox data
  const loadCashbox = async () => {
    if (!isMounted.current) return;

    try {
      setLoading(true);
      setError(null);

      console.log('Attempting to load cashbox data');
      const cashboxData = await window.api.cashbox.get();
      console.log('Received cashbox data:', cashboxData);

      if (!isMounted.current) return;

      // Ensure we have a valid cashbox object
      if (cashboxData && typeof cashboxData === 'object') {
        setCashbox(cashboxData);

        // Load transactions if cashbox exists
        if (cashboxData.exists) {
          await loadTransactions();
        }
      } else {
        console.error('Invalid cashbox data received:', cashboxData);
        setError('تم استلام بيانات غير صالحة للخزينة');
      }

      setLoading(false);
    } catch (error) {
      console.error('Error loading cashbox data:', error);

      if (isMounted.current) {
        setError('حدث خطأ أثناء تحميل بيانات الخزينة');
        setLoading(false);
      }
    }
  };

  // Load transactions
  const loadTransactions = async () => {
    if (!isMounted.current) return;

    try {
      console.log('Attempting to load cashbox transactions with filters:', filters);
      const transactionsData = await window.api.cashbox.getTransactions(filters);
      console.log('Received cashbox transactions data:', transactionsData);

      if (!isMounted.current) return;

      // Ensure we have an array even if the response is null or undefined
      setTransactions(Array.isArray(transactionsData) ? transactionsData : []);
    } catch (error) {
      console.error('Error loading transactions:', error);
      showNotification('حدث خطأ أثناء تحميل المعاملات', 'error');
      // Set empty array on error to prevent undefined errors
      setTransactions([]);
    }
  };

  // Create new cashbox
  const handleCreateCashbox = async () => {
    if (!initialBalance || isNaN(initialBalance) || initialBalance < 0) {
      showNotification('الرجاء إدخال رصيد افتتاحي صحيح', 'error');
      return;
    }

    try {
      setLoading(true);
      const result = await window.api.cashbox.create(Number(initialBalance));

      if (!isMounted.current) return;

      if (result.success) {
        setCashbox(result.cashbox);
        showNotification('تم إنشاء الخزينة بنجاح', 'success');
        setShowInitialBalanceModal(false);
        await loadTransactions();
      }
    } catch (error) {
      console.error('Error creating cashbox:', error);
      if (isMounted.current) {
        showNotification('حدث خطأ أثناء إنشاء الخزينة', 'error');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };



  // Add new transaction
  const handleAddTransaction = async () => {
    // Validate form data
    if (!transactionForm.amount || isNaN(transactionForm.amount) || transactionForm.amount <= 0) {
      showNotification('الرجاء إدخال مبلغ صحيح', 'error');
      return;
    }

    if (!transactionForm.source) {
      showNotification('الرجاء إدخال مصدر المعاملة', 'error');
      return;
    }

    try {
      setLoading(true);

      const transaction = {
        ...transactionForm,
        amount: Number(transactionForm.amount)
      };

      // إذا كانت المعاملة دخل، نقوم بعرض معلومات توضيحية للمستخدم
      if (transaction.type === 'income' && cashbox) {
        const currentBalance = cashbox.current_balance;
        const initialBalance = cashbox.initial_balance;
        const maxAddableAmount = Math.max(0, initialBalance - currentBalance);
        const amountToAddToBalance = Math.min(transaction.amount, maxAddableAmount);
        const excessAmount = Math.max(0, transaction.amount - amountToAddToBalance);

        // إذا كان هناك مبلغ زائد سيتم تحويله للأرباح، نعرض رسالة توضيحية
        if (excessAmount > 0) {
          console.log(`سيتم إضافة ${amountToAddToBalance} إلى الرصيد الحالي و ${excessAmount} إلى الأرباح`);
        }
      }

      const result = await window.api.cashbox.addTransaction(transaction);

      if (!isMounted.current) return;

      if (result.success) {
        // Update cashbox and transactions
        setCashbox(result.cashbox);
        await loadTransactions();

        // Reset form and close modal
        setTransactionForm({
          type: 'income',
          amount: '',
          source: '',
          notes: ''
        });
        setShowAddTransactionModal(false);

        showNotification('تمت إضافة المعاملة بنجاح', 'success');
      }
    } catch (error) {
      console.error('Error adding transaction:', error);
      if (isMounted.current) {
        showNotification(error.message || 'حدث خطأ أثناء إضافة المعاملة', 'error');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  // Apply filters
  const applyFilters = async () => {
    try {
      await loadTransactions();
      if (isMounted.current) {
        setShowFilters(false);
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      showNotification('حدث خطأ أثناء تطبيق التصفية', 'error');
    }
  };

  // Reset filters
  const resetFilters = () => {
    if (!isMounted.current) return;

    setFilters({
      type: '',
      source: '',
      startDate: '',
      endDate: ''
    });
    loadTransactions();
    setShowFilters(false);
  };

  // Format date (Gregorian format)
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  // Helper functions for transaction display
  const getAmountColor = (type) => type === 'income' ? 'text-green-600' : 'text-red-600';

  const getTransactionIcon = (type) => (
    type === 'income'
      ? <FaArrowUp className="text-green-600" />
      : <FaArrowDown className="text-red-600" />
  );

  const getTransactionTypeText = (type) => type === 'income' ? 'دخل' : 'مصروفات';

  return (
    <div className="cashbox-page">
      <div className="cashbox-header">
        <h1>الخزينة</h1>
        <p>إدارة الخزينة والمعاملات المالية</p>
      </div>

      {loading && <div className="loading">جاري التحميل...</div>}

      {error && <div className="error">{error}</div>}

      {!loading && !error && (
        <>
          {/* عرض معلومات الخزينة */}
          <div className="cashbox-info">
            {cashbox && cashbox.exists ? (
              <div className="cashbox-cards">
                <Card
                  title="الرصيد الافتتاحي"
                  icon={<FaWallet />}
                  className="cashbox-card"
                  actions={
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        setInitialBalance(cashbox.initial_balance);
                        setShowEditInitialBalanceModal(true);
                        // إعادة تمكين حقول النموذج
                        setTimeout(enableFormFields, 100);
                      }}
                    >
                      تعديل
                    </Button>
                  }
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.initial_balance} />
                  </div>
                </Card>

                <Card title="الرصيد الحالي" icon={<FaWallet />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.current_balance} />
                  </div>
                </Card>

                <Card title="إجمالي المبيعات" icon={<FaArrowUp />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.sales_total || 0} />
                  </div>
                </Card>

                <Card title="إجمالي المشتريات" icon={<FaArrowDown />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.purchases_total || 0} />
                  </div>
                </Card>

                <Card title="إجمالي الأرباح" icon={<FaChartLine />} className="cashbox-card profit-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.profit_total || 0} />
                  </div>
                </Card>
              </div>
            ) : (
              <div className="no-cashbox">
                <p>لا يوجد خزينة مسجلة بعد. قم بإنشاء خزينة جديدة لبدء تسجيل المعاملات المالية.</p>
                <Button
                  variant="primary"
                  icon={<FaPlus />}
                  onClick={() => {
                    setShowInitialBalanceModal(true);
                    setTimeout(enableFormFields, 100);
                  }}
                  disabled={loading}
                >
                  إنشاء خزينة جديدة
                </Button>
              </div>
            )}
          </div>

          {/* عرض المعاملات */}
          {cashbox && cashbox.exists && (
            <Card
              title="المعاملات المالية"
              icon={<FaMoneyBillWave />}
              actions={
                <>
                  <Button
                    variant="secondary"
                    icon={<FaFilter />}
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    تصفية
                  </Button>
                  <Button
                    variant="primary"
                    icon={<FaPlus />}
                    onClick={() => {
                      setShowAddTransactionModal(true);
                      setTimeout(enableFormFields, 100);
                    }}
                    disabled={loading}
                  >
                    إضافة معاملة
                  </Button>
                </>
              }
            >
              {/* نموذج التصفية */}
              {showFilters && (
                <div className="filter-form">
                  <div className="filter-row">
                    <div className="filter-group">
                      <label>نوع المعاملة</label>
                      <select
                        value={filters.type}
                        onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                      >
                        <option value="">الكل</option>
                        <option value="income">دخل</option>
                        <option value="expense">مصروفات</option>
                      </select>
                    </div>

                    <div className="filter-group">
                      <label>المصدر</label>
                      <input
                        type="text"
                        value={filters.source}
                        onChange={(e) => setFilters({ ...filters, source: e.target.value })}
                        placeholder="بحث في المصدر"
                      />
                    </div>
                  </div>

                  <div className="filter-row">
                    <div className="filter-group">
                      <label>من تاريخ</label>
                      <input
                        type="date"
                        value={filters.startDate}
                        onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                      />
                    </div>

                    <div className="filter-group">
                      <label>إلى تاريخ</label>
                      <input
                        type="date"
                        value={filters.endDate}
                        onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="filter-actions">
                    <Button variant="secondary" onClick={resetFilters}>إعادة تعيين</Button>
                    <Button variant="primary" onClick={applyFilters}>تطبيق</Button>
                  </div>
                </div>
              )}

              {/* جدول المعاملات */}
              <div className="transactions-table-container">
                {transactions.length === 0 ? (
                  <div className="no-transactions">
                    <p>لا توجد معاملات مسجلة بعد.</p>
                  </div>
                ) : (
                  <table className="transactions-table">
                    <thead>
                      <tr>
                        <th>#</th>
                        <th>النوع</th>
                        <th>المبلغ</th>
                        <th>المصدر</th>
                        <th>التاريخ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((transaction, index) => (
                        <tr key={transaction.id}>
                          <td>{index + 1}</td>
                          <td>
                            <div className="transaction-type">
                              {getTransactionIcon(transaction.type)}
                              <span>{getTransactionTypeText(transaction.type)}</span>
                            </div>
                          </td>
                          <td className={getAmountColor(transaction.type)}>
                            <FormattedCurrency amount={transaction.amount} />
                          </td>
                          <td>{transaction.source}</td>
                          <td>{formatDate(transaction.created_at)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </Card>
          )}
        </>
      )}

      {/* نافذة إنشاء خزينة جديدة */}
      <Modal
        isOpen={showInitialBalanceModal}
        onClose={() => setShowInitialBalanceModal(false)}
        title="إنشاء خزينة جديدة"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>الرصيد الافتتاحي</label>
            <input
              type="number"
              value={initialBalance}
              onChange={(e) => setInitialBalance(e.target.value)}
              placeholder="أدخل الرصيد الافتتاحي"
              min="0"
              step="0.01"
            />
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowInitialBalanceModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateCashbox}
              disabled={loading}
            >
              إنشاء
            </Button>
          </div>
        </div>
      </Modal>

      {/* نافذة تعديل الرصيد الافتتاحي */}
      <Modal
        isOpen={showEditInitialBalanceModal}
        onClose={() => setShowEditInitialBalanceModal(false)}
        title="تعديل الرصيد الافتتاحي"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>الرصيد الافتتاحي الجديد</label>
            <input
              type="number"
              value={initialBalance}
              onChange={(e) => setInitialBalance(e.target.value)}
              placeholder="أدخل الرصيد الافتتاحي الجديد"
              min="0"
              step="0.01"
            />
            <p className="form-hint">
              ملاحظة: تعديل الرصيد الافتتاحي سيؤثر على حسابات الأرباح.
            </p>
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowEditInitialBalanceModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={async () => {
                if (!initialBalance || isNaN(initialBalance) || initialBalance < 0) {
                  showNotification('الرجاء إدخال رصيد افتتاحي صحيح', 'error');
                  return;
                }

                try {
                  setLoading(true);
                  await updateCashboxInitialBalance(Number(initialBalance));

                  if (!isMounted.current) return;

                  showNotification('تم تحديث الرصيد الافتتاحي بنجاح', 'success');
                  setShowEditInitialBalanceModal(false);
                  await loadCashbox();
                } catch (error) {
                  console.error('Error updating initial balance:', error);
                  if (isMounted.current) {
                    showNotification('حدث خطأ أثناء تحديث الرصيد الافتتاحي', 'error');
                  }
                } finally {
                  if (isMounted.current) {
                    setLoading(false);
                  }
                }
              }}
              disabled={loading}
            >
              حفظ التغييرات
            </Button>
          </div>
        </div>
      </Modal>

      {/* نافذة إضافة معاملة جديدة */}
      <Modal
        isOpen={showAddTransactionModal}
        onClose={() => setShowAddTransactionModal(false)}
        title="إضافة معاملة جديدة"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>نوع المعاملة</label>
            <div className="transaction-type-buttons">
              <button
                className={`type-button ${transactionForm.type === 'income' ? 'active' : ''}`}
                onClick={() => setTransactionForm({ ...transactionForm, type: 'income' })}
              >
                <FaArrowUp /> دخل
              </button>
              <button
                className={`type-button ${transactionForm.type === 'expense' ? 'active' : ''}`}
                onClick={() => setTransactionForm({ ...transactionForm, type: 'expense' })}
              >
                <FaArrowDown /> مصروفات
              </button>
            </div>
          </div>

          <div className="form-group">
            <label>المبلغ</label>
            <input
              type="number"
              value={transactionForm.amount}
              onChange={(e) => setTransactionForm({ ...transactionForm, amount: e.target.value })}
              placeholder="أدخل المبلغ"
              min="0.01"
              step="0.01"
            />
            {transactionForm.type === 'income' && cashbox && (
              <p className="form-hint">
                ملاحظة: إذا تجاوز المبلغ الحد الأقصى للرصيد الافتتاحي، سيتم تحويل المبلغ الزائد تلقائيًا إلى الأرباح.
                <br />
                الرصيد الحالي: {cashbox.current_balance} | الرصيد الافتتاحي: {cashbox.initial_balance}
                <br />
                الحد الأقصى للإضافة إلى الرصيد الحالي: {Math.max(0, cashbox.initial_balance - cashbox.current_balance)}
              </p>
            )}
          </div>

          <div className="form-group">
            <label>المصدر</label>
            <input
              type="text"
              value={transactionForm.source}
              onChange={(e) => setTransactionForm({ ...transactionForm, source: e.target.value })}
              placeholder="أدخل مصدر المعاملة"
            />
          </div>

          <div className="form-group">
            <label>ملاحظات (اختياري)</label>
            <textarea
              value={transactionForm.notes}
              onChange={(e) => setTransactionForm({ ...transactionForm, notes: e.target.value })}
              placeholder="أدخل ملاحظات إضافية"
              rows="3"
            />
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowAddTransactionModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleAddTransaction}
              disabled={loading}
            >
              إضافة
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Cashbox;
