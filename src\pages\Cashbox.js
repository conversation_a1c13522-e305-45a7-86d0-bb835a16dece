import React, { useState, useEffect, useRef } from 'react';
import {
  FaWallet,
  FaMoneyBillWave,
  FaPlus,
  FaArrowUp,
  FaArrowDown,
  FaFilter,
  FaExchangeAlt,
  FaTools,
  FaTrash,
  FaTruck,
  FaChartLine
} from 'react-icons/fa';
import Card from '../components/Card';
import Button from '../components/Button';
import Modal from '../components/Modal';
import FormattedCurrency from '../components/FormattedCurrency';
import { useApp } from '../context/AppContext';
import './Cashbox.css';

const Cashbox = () => {
  // State variables
  const [cashbox, setCashbox] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [transactions, setTransactions] = useState([]);

  // Modal states
  const [showInitialBalanceModal, setShowInitialBalanceModal] = useState(false);
  const [showEditInitialBalanceModal, setShowEditInitialBalanceModal] = useState(false);
  const [showAddTransactionModal, setShowAddTransactionModal] = useState(false);
  const [initialBalance, setInitialBalance] = useState(0);
  const [transactionForm, setTransactionForm] = useState({
    type: 'income',
    amount: '',
    source: '',
    notes: ''
  });

  // Filter states - تم تعديل الفلاتر لعرض جميع المعاملات افتراضيًا
  const [filters, setFilters] = useState({
    type: '',
    source: '', // تم تعيين قيمة فارغة لعرض جميع المعاملات
    startDate: '',
    endDate: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  // App context
  const { showNotification, updateCashboxInitialBalance } = useApp();

  // Refs
  const formRef = useRef(null);
  const isMounted = useRef(true);

  // دالة تحديث الخزينة مباشرة من قاعدة البيانات
  const updateCashboxDirectly = async () => {
    try {
      console.log('[CASHBOX-DIRECT] بدء تحديث الخزينة مباشرة من قاعدة البيانات');

      // استعلام مباشر للحصول على بيانات الخزينة
      const query = `
        SELECT * FROM cashbox LIMIT 1
      `;

      // تنفيذ الاستعلام باستخدام IPC
      const result = await window.api.invoke('execute-direct-query', { query });

      if (result && result.success && result.data && result.data.length > 0) {
        const cashboxData = result.data[0];
        console.log('[CASHBOX-DIRECT] تم الحصول على بيانات الخزينة مباشرة:', cashboxData);

        // تحديث حالة الخزينة مباشرة
        setCashbox({
          id: cashboxData.id,
          initial_balance: Number(cashboxData.initial_balance || 0),
          current_balance: Number(cashboxData.current_balance || 0),
          profit_total: Number(cashboxData.profit_total || 0),
          sales_total: Number(cashboxData.sales_total || 0),
          purchases_total: Number(cashboxData.purchases_total || 0),
          returns_total: Number(cashboxData.returns_total || 0),
          transport_total: Number(cashboxData.transport_total || 0),
          updated_at: cashboxData.updated_at || new Date().toISOString(),
          exists: true
        });

        console.log('[CASHBOX-DIRECT] تم تحديث حالة الخزينة مباشرة');
        return true;
      } else {
        console.error('[CASHBOX-DIRECT] لم يتم العثور على بيانات الخزينة');
        return false;
      }
    } catch (error) {
      console.error('[CASHBOX-DIRECT] خطأ في تحديث الخزينة مباشرة:', error);
      return false;
    }
  };

  // Component mount/unmount lifecycle
  useEffect(() => {
    isMounted.current = true;

    // إضافة CSS لتحسين مظهر المعاملات
    const style = document.createElement('style');
    style.textContent = `
      .transaction-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
      .transaction-item-name {
        font-weight: bold;
        color: #2563eb;
      }
      .transaction-quantity {
        font-size: 0.85rem;
        color: #4b5563;
      }
      .transaction-invoice {
        font-size: 0.85rem;
        color: #4b5563;
      }
      .transaction-items-count {
        font-size: 0.8rem;
        color: #6b7280;
        margin-right: 5px;
      }
      .transactions-table td {
        vertical-align: top;
        padding: 10px 8px;
      }
    `;
    document.head.appendChild(style);

    // Load cashbox data on component mount
    loadCashbox();

    // إنشاء مؤقت لتحديث الخزينة كل 5 ثوانٍ
    const updateInterval = setInterval(() => {
      if (isMounted.current) {
        console.log('[CASHBOX-DIRECT] تحديث دوري للخزينة');
        updateCashboxDirectly();
      }
    }, 5000);

    // تعريف وظيفة loadCashboxInfo للاستخدام في مستمعي الأحداث
    window.loadCashboxInfo = () => {
      console.log('[CASHBOX-FIX] تم استدعاء loadCashboxInfo من مستمع الأحداث');
      if (isMounted.current) {
        updateCashboxDirectly();
      }
    };

    // تعريف وظيفة loadCashbox للاستخدام في مستمعي الأحداث
    window.loadCashbox = () => {
      console.log('[CASHBOX-FIX] تم استدعاء window.loadCashbox من مستمع الأحداث');
      if (isMounted.current) {
        updateCashboxDirectly();
      }
    };

    // إضافة مستمع للإشعار المباشر لتحديث الخزينة
    const handleDirectCashboxUpdate = (event) => {
      console.log('[CASHBOX-FIX] تم استلام حدث direct-cashbox-update في صفحة الخزينة:', event.detail);

      if (isMounted.current) {
        // تحديث الخزينة مباشرة من قاعدة البيانات
        updateCashboxDirectly();

        // تحميل المعاملات بعد تحديث الخزينة
        loadTransactions();
      }
    };

    // إضافة مستمع للإشعار المباشر
    window.addEventListener('direct-cashbox-update', handleDirectCashboxUpdate);

    // إضافة مستمع لحدث تحديث الخزينة
    const handleCashboxUpdated = (data) => {
      console.log('[CASHBOX-FIX] تم استلام حدث تحديث الخزينة في صفحة الخزينة:', data);

      // تسجيل معلومات إضافية للتشخيص
      if (data) {
        console.log('[CASHBOX-FIX] نوع المعاملة:', data.transaction_type || 'غير محدد');
        console.log('[CASHBOX-FIX] المبلغ:', data.amount || 0);
        console.log('[CASHBOX-FIX] الرصيد الحالي:', data.current_balance || 'غير محدد');
        console.log('[CASHBOX-FIX] إجمالي المبيعات:', data.sales_total || 'غير محدد');
        console.log('[CASHBOX-FIX] إجمالي المشتريات:', data.purchases_total || 'غير محدد');
        console.log('[CASHBOX-FIX] إجمالي المرتجعات:', data.returns_total || 'غير محدد');
        console.log('[CASHBOX-FIX] إجمالي مصاريف النقل:', data.transport_total || 'غير محدد');
        console.log('[CASHBOX-FIX] إجمالي الأرباح:', data.profit_total || 'غير محدد');
      }

      if (isMounted.current) {
        console.log('[CASHBOX-FIX] جاري تحديث بيانات الخزينة...');

        // تحديث حالة الخزينة مباشرة إذا كانت البيانات متوفرة
        if (data && data.current_balance !== undefined) {
          console.log('[CASHBOX-FIX] تحديث حالة الخزينة مباشرة من البيانات المستلمة');
          setCashbox({
            ...cashbox,
            current_balance: data.current_balance,
            sales_total: data.sales_total,
            purchases_total: data.purchases_total,
            returns_total: data.returns_total,
            transport_total: data.transport_total,
            profit_total: data.profit_total,
            updated_at: data.timestamp || new Date().toISOString()
          });
        }

        // تحميل بيانات الخزينة من الخادم
        loadCashbox();
      }
    };

    // إضافة مستمع لحدث الحاجة للتحديث
    const handleRefreshNeeded = (data) => {
      if (data && data.target === 'cashbox' && isMounted.current) {
        console.log('[CASHBOX-FIX] تم استلام حدث الحاجة لتحديث الخزينة:', data);

        // إذا كان الإشعار يحتوي على علامة force_refresh، نقوم بتحديث الخزينة فورًا
        if (data.force_refresh) {
          console.log('[CASHBOX-FIX] تحديث إجباري للخزينة');
          loadCashbox();
        } else {
          // تحديث الخزينة بعد تأخير قصير
          setTimeout(() => {
            if (isMounted.current) {
              console.log('[CASHBOX-FIX] تحديث الخزينة بعد تأخير قصير');
              loadCashbox();
            }
          }, 100);
        }
      } else if (data && data.target === 'all' && isMounted.current) {
        console.log('[CASHBOX-FIX] تم استلام حدث الحاجة لتحديث كل شيء:', data);
        loadCashbox();
      }
    };

    if (window.api && window.api.on) {
      // تسجيل المستمعين
      window.api.on('cashbox-updated', handleCashboxUpdated);
      window.api.on('refresh-needed', handleRefreshNeeded);
      console.log('[CASHBOX-FIX] تم إضافة مستمعي أحداث تحديث الخزينة بنجاح');
    }

    // Cleanup on unmount
    return () => {
      isMounted.current = false;

      // إزالة مستمعي الأحداث
      if (window.api && window.api.removeListener) {
        window.api.removeListener('cashbox-updated', handleCashboxUpdated);
        window.api.removeListener('refresh-needed', handleRefreshNeeded);
        console.log('[CASHBOX-FIX] تم إزالة مستمعي أحداث تحديث الخزينة');
      }

      // إزالة مستمع الإشعار المباشر
      window.removeEventListener('direct-cashbox-update', handleDirectCashboxUpdate);
      console.log('[CASHBOX-FIX] تم إزالة مستمع الإشعار المباشر');

      // إزالة المؤقت
      clearInterval(updateInterval);
      console.log('[CASHBOX-DIRECT] تم إزالة المؤقت');

      // إزالة وظائف التحديث
      delete window.loadCashboxInfo;
      delete window.loadCashbox;
    };
  }, []);

  // Enable form fields after modal opens
  const enableFormFields = () => {
    if (formRef.current && isMounted.current) {
      const inputs = formRef.current.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.disabled = false;
      });
    }
  };

  // Load cashbox data
  const loadCashbox = async () => {
    if (!isMounted.current) return;

    try {
      setLoading(true);
      setError(null);

      console.log('[CASHBOX-FIX] جاري تحميل بيانات الخزينة...');

      // التحقق من وجود window.api.cashbox
      if (!window.api || !window.api.cashbox) {
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // محاولة الحصول على بيانات الخزينة مع إعادة المحاولة
      let cashboxData = null;
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts && !cashboxData && isMounted.current) {
        attempts++;
        try {
          console.log(`[CASHBOX-FIX] محاولة تحميل بيانات الخزينة (${attempts}/${maxAttempts})...`);
          cashboxData = await window.api.cashbox.get();
          console.log('[CASHBOX-FIX] تم استلام بيانات الخزينة:', cashboxData);
        } catch (attemptError) {
          console.error(`[CASHBOX-FIX] خطأ في محاولة تحميل بيانات الخزينة (${attempts}/${maxAttempts}):`, attemptError);

          if (attempts < maxAttempts) {
            // انتظار قبل المحاولة التالية
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            throw attemptError;
          }
        }
      }

      if (!isMounted.current) return;

      // التأكد من أن لدينا كائن خزينة صالح
      if (cashboxData && typeof cashboxData === 'object') {
        // تسجيل قيم الخزينة للتشخيص
        console.log('[CASHBOX-FIX] قيم الخزينة المستلمة:');
        console.log('- الرصيد الافتتاحي:', cashboxData.initial_balance);
        console.log('- الرصيد الحالي:', cashboxData.current_balance);
        console.log('- إجمالي الأرباح:', cashboxData.profit_total);
        console.log('- إجمالي المبيعات:', cashboxData.sales_total);
        console.log('- إجمالي المشتريات:', cashboxData.purchases_total);
        console.log('- إجمالي المرتجعات:', cashboxData.returns_total);
        console.log('- إجمالي مصاريف النقل:', cashboxData.transport_total);

        // تحديث حالة الخزينة
        console.log('[CASHBOX-FIX] تحديث حالة الخزينة...');

        // تحديث حالة الخزينة بشكل مباشر
        setCashbox(prevCashbox => {
          // إذا كان هناك خزينة سابقة، نحتفظ ببعض الخصائص منها
          if (prevCashbox && typeof prevCashbox === 'object') {
            return {
              ...prevCashbox,
              ...cashboxData,
              // التأكد من أن القيم الرقمية صحيحة
              initial_balance: Number(cashboxData.initial_balance || 0),
              current_balance: Number(cashboxData.current_balance || 0),
              profit_total: Number(cashboxData.profit_total || 0),
              sales_total: Number(cashboxData.sales_total || 0),
              purchases_total: Number(cashboxData.purchases_total || 0),
              returns_total: Number(cashboxData.returns_total || 0),
              transport_total: Number(cashboxData.transport_total || 0),
              updated_at: cashboxData.updated_at || new Date().toISOString()
            };
          }

          // إذا لم تكن هناك خزينة سابقة، نستخدم البيانات الجديدة مباشرة
          return cashboxData;
        });

        // تحميل المعاملات إذا كانت الخزينة موجودة
        if (cashboxData.exists) {
          console.log('[CASHBOX-FIX] الخزينة موجودة، جاري تحميل المعاملات...');
          await loadTransactions();
        } else {
          console.log('[CASHBOX-FIX] الخزينة غير موجودة');
        }

        // إرسال إشعار بتحديث الخزينة للتأكد من تحديث البطاقات
        if (window.dispatchEvent) {
          console.log('[CASHBOX-FIX] إرسال حدث تحديث الخزينة للمتصفح');
          window.dispatchEvent(new CustomEvent('cashbox-updated-ui', {
            detail: cashboxData
          }));
        }
      } else {
        console.error('[CASHBOX-FIX] تم استلام بيانات غير صالحة للخزينة:', cashboxData);
        setError('تم استلام بيانات غير صالحة للخزينة');
      }

      if (isMounted.current) {
        setLoading(false);
      }
    } catch (error) {
      console.error('[CASHBOX-FIX] خطأ في تحميل بيانات الخزينة:', error);

      if (isMounted.current) {
        setError('حدث خطأ أثناء تحميل بيانات الخزينة');
        setLoading(false);
      }
    }
  };

  // Load transactions
  const loadTransactions = async () => {
    if (!isMounted.current) return;

    try {
      // التأكد من أن الفلاتر تسمح بعرض جميع المعاملات
      if (filters.source !== '' || filters.type !== '') {
        setFilters(prev => ({ ...prev, source: '', type: '' }));
      }
      console.log('[CASHBOX-FIX] جاري تحميل معاملات الخزينة مع الفلاتر:', filters);

      // التحقق من وجود window.api.cashbox
      if (!window.api || !window.api.cashbox) {
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // محاولة الحصول على المعاملات مع إعادة المحاولة
      let transactionsData = null;
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts && transactionsData === null && isMounted.current) {
        attempts++;
        try {
          console.log(`[CASHBOX-FIX] محاولة تحميل المعاملات (${attempts}/${maxAttempts})...`);
          transactionsData = await window.api.cashbox.getTransactions(filters);
          console.log(`[CASHBOX-FIX] تم استلام ${transactionsData ? transactionsData.length : 0} معاملة للخزينة`);
        } catch (attemptError) {
          console.error(`[CASHBOX-FIX] خطأ في محاولة تحميل المعاملات (${attempts}/${maxAttempts}):`, attemptError);

          // إذا كان الخطأ بسبب عدم وجود خزينة، نتوقف عن المحاولة
          if (attemptError.message && attemptError.message.includes('لا يوجد سجل للخزينة')) {
            console.log('[CASHBOX-FIX] لم يتم العثور على خزينة، عرض قائمة معاملات فارغة');
            transactionsData = [];
            break;
          }

          if (attempts < maxAttempts) {
            // انتظار قبل المحاولة التالية
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            throw attemptError;
          }
        }
      }

      if (!isMounted.current) return;

      // تحليل أنواع المعاملات المستلمة
      if (transactionsData && transactionsData.length > 0) {
        // تسجيل أنواع المعاملات المستلمة
        const typeCount = {};
        const sourceCount = {};
        const transactionSourceCount = {};

        transactionsData.forEach(transaction => {
          // حساب أنواع المعاملات
          if (!typeCount[transaction.type]) {
            typeCount[transaction.type] = 0;
          }
          typeCount[transaction.type]++;

          // حساب مصادر المعاملات
          if (!sourceCount[transaction.source]) {
            sourceCount[transaction.source] = 0;
          }
          sourceCount[transaction.source]++;

          // حساب مصادر البيانات
          const source = transaction.transaction_source || 'unknown';
          if (!transactionSourceCount[source]) {
            transactionSourceCount[source] = 0;
          }
          transactionSourceCount[source]++;
        });

        console.log('[CASHBOX-FIX] إحصائيات المعاملات المستلمة:');
        console.log('- أنواع المعاملات:', typeCount);
        console.log('- مصادر المعاملات:', sourceCount);
        console.log('- مصادر البيانات:', transactionSourceCount);
      }

      // التأكد من أن لدينا مصفوفة حتى لو كانت الاستجابة فارغة أو غير معرفة
      if (isMounted.current) {
        console.log('[CASHBOX-FIX] تحديث قائمة المعاملات في واجهة المستخدم');
        setTransactions(Array.isArray(transactionsData) ? transactionsData : []);
      }
    } catch (error) {
      console.error('[CASHBOX-FIX] خطأ في تحميل المعاملات:', error);

      if (isMounted.current) {
        // إذا كان الخطأ بسبب عدم وجود خزينة، نتجاهله ونعرض قائمة فارغة
        if (error.message && error.message.includes('لا يوجد سجل للخزينة')) {
          console.log('[CASHBOX-FIX] لم يتم العثور على خزينة، عرض قائمة معاملات فارغة');
          setTransactions([]);
        } else {
          showNotification('حدث خطأ أثناء تحميل المعاملات', 'error');
          // تعيين مصفوفة فارغة في حالة الخطأ لمنع أخطاء غير معرفة
          setTransactions([]);
        }
      }
    }
  };

  // Recalculate profits
  const handleRecalculateProfits = async () => {
    try {
      setLoading(true);
      console.log('[CASHBOX-PROFITS] بدء إعادة حساب الأرباح...');

      // استدعاء دالة إعادة حساب الأرباح
      const result = await window.api.invoke('fix-cashbox-from-transactions');

      if (result && result.success) {
        console.log('[CASHBOX-PROFITS] تم إعادة حساب الأرباح بنجاح:', result);
        showNotification('تم إعادة حساب الأرباح بنجاح', 'success');

        // إعادة تحميل بيانات الخزينة
        await loadCashbox();
      } else {
        throw new Error(result?.error || 'فشل في إعادة حساب الأرباح');
      }
    } catch (error) {
      console.error('[CASHBOX-PROFITS] خطأ في إعادة حساب الأرباح:', error);
      showNotification('حدث خطأ أثناء إعادة حساب الأرباح', 'error');
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  // Create new cashbox
  const handleCreateCashbox = async () => {
    if (!initialBalance || isNaN(initialBalance) || initialBalance < 0) {
      showNotification('الرجاء إدخال رصيد افتتاحي صحيح', 'error');
      return;
    }

    try {
      setLoading(true);
      const result = await window.api.cashbox.create(Number(initialBalance));

      if (!isMounted.current) return;

      if (result.success) {
        setCashbox(result.cashbox);
        showNotification('تم إنشاء الخزينة بنجاح', 'success');
        setShowInitialBalanceModal(false);
        await loadTransactions();
      }
    } catch (error) {
      console.error('Error creating cashbox:', error);
      if (isMounted.current) {
        showNotification('حدث خطأ أثناء إنشاء الخزينة', 'error');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  // Add new transaction
  const handleAddTransaction = async () => {
    // Validate form data
    if (!transactionForm.amount || isNaN(transactionForm.amount) || transactionForm.amount <= 0) {
      showNotification('الرجاء إدخال مبلغ صحيح', 'error');
      return;
    }

    if (!transactionForm.source) {
      showNotification('الرجاء إدخال مصدر المعاملة', 'error');
      return;
    }

    try {
      setLoading(true);

      const transaction = {
        ...transactionForm,
        amount: Number(transactionForm.amount)
      };

      // إذا كانت المعاملة دخل وكانت هناك خزينة، نقوم بعرض معلومات توضيحية للمستخدم
      if (transaction.type === 'income' && cashbox && cashbox.exists) {
        const currentBalance = cashbox.current_balance;
        const initialBalance = cashbox.initial_balance;
        const maxAddableAmount = Math.max(0, initialBalance - currentBalance);
        const amountToAddToBalance = Math.min(transaction.amount, maxAddableAmount);
        const excessAmount = Math.max(0, transaction.amount - amountToAddToBalance);

        // إذا كان هناك مبلغ زائد سيتم تحويله للأرباح، نعرض رسالة توضيحية
        if (excessAmount > 0) {
          console.log(`سيتم إضافة ${amountToAddToBalance} إلى الرصيد الحالي و ${excessAmount} إلى الأرباح`);
        }
      }

      const result = await window.api.cashbox.addTransaction(transaction);

      if (!isMounted.current) return;

      if (result.success) {
        // Update cashbox and transactions
        setCashbox(result.cashbox);

        // إذا كان هناك رسالة في النتيجة، نعرضها للمستخدم
        if (result.message) {
          showNotification(result.message, 'info');
        }

        // تحميل المعاملات
        await loadTransactions();

        // Reset form and close modal
        setTransactionForm({
          type: 'income',
          amount: '',
          source: '',
          notes: ''
        });
        setShowAddTransactionModal(false);

        showNotification('تمت إضافة المعاملة بنجاح', 'success');

        // تحديث الصفحة لعرض الخزينة الموجودة بعد إضافة المعاملة
        await loadCashbox();
      }
    } catch (error) {
      console.error('Error adding transaction:', error);
      if (isMounted.current) {
        showNotification(error.message || 'حدث خطأ أثناء إضافة المعاملة', 'error');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  // Apply filters
  const applyFilters = async () => {
    try {
      await loadTransactions();
      if (isMounted.current) {
        setShowFilters(false);
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      showNotification('حدث خطأ أثناء تطبيق التصفية', 'error');
    }
  };

  // Reset filters
  const resetFilters = () => {
    if (!isMounted.current) return;

    // إعادة تعيين جميع الفلاتر
    setFilters({
      type: '',
      source: '',
      startDate: '',
      endDate: ''
    });

    // تحميل المعاملات بعد إعادة تعيين الفلاتر
    console.log('إعادة تعيين جميع الفلاتر وتحميل المعاملات');
    loadTransactions();
    setShowFilters(false);
  };

  // Format date (Gregorian format)
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  // Helper functions for transaction display
  const getAmountColor = (type, source) => {
    // استخدام نوع المعاملة الأصلي من الحقل source أو type
    const transactionType = source || type;

    // تحديد اللون بناءً على نوع المعاملة
    if (transactionType === 'sale' || transactionType === 'income' || transactionType === 'delete-expense') {
      return 'text-green-600'; // أخضر للمبيعات والدخل وحذف المصروفات
    } else if (transactionType === 'purchase' || transactionType === 'expense' || transactionType === 'delete-income') {
      return 'text-red-600'; // أحمر للمشتريات والمصروفات وحذف الدخل
    } else if (transactionType === 'transport') {
      return 'text-orange-600'; // برتقالي لمصاريف النقل
    } else if (transactionType === 'return') {
      return 'text-orange-600'; // برتقالي للمرتجعات
    } else if (transactionType === 'fix') {
      return 'text-blue-600'; // أزرق للتصحيح
    }

    // اللون الافتراضي
    return 'text-gray-600';
  };

  const getTransactionIcon = (type, source) => {
    // استخدام نوع المعاملة الأصلي من الحقل source أو type
    const transactionType = source || type;

    // تحديد الأيقونة بناءً على نوع المعاملة
    if (transactionType === 'sale' || transactionType === 'income') {
      return <FaArrowUp className="text-green-600" />; // سهم لأعلى للمبيعات والدخل
    } else if (transactionType === 'purchase' || transactionType === 'expense') {
      return <FaArrowDown className="text-red-600" />; // سهم لأسفل للمشتريات والمصروفات
    } else if (transactionType === 'transport') {
      return <FaTruck className="text-orange-600" />; // أيقونة شاحنة لمصاريف النقل
    } else if (transactionType === 'return') {
      return <FaExchangeAlt className="text-orange-600" />; // أيقونة تبادل للمرتجعات
    } else if (transactionType === 'fix') {
      return <FaTools className="text-blue-600" />; // أيقونة أدوات للتصحيح
    } else if (transactionType === 'delete-income' || transactionType === 'delete-expense') {
      return <FaTrash className="text-gray-600" />; // أيقونة سلة المهملات للحذف
    }

    // الأيقونة الافتراضية
    return <FaArrowDown className="text-gray-600" />;
  };

  const getTransactionTypeText = (type, source) => {
    // استخدام نوع المعاملة الأصلي من الحقل source أو type
    const transactionType = source || type;

    // تحديد النص بناءً على نوع المعاملة
    if (transactionType === 'sale') {
      return 'مبيعات';
    } else if (transactionType === 'purchase') {
      return 'مشتريات';
    } else if (transactionType === 'transport') {
      return 'مصاريف نقل';
    } else if (transactionType === 'return') {
      if (type === 'income') {
        return 'مرتجعات مشتريات';
      } else if (type === 'expense') {
        return 'مرتجعات مبيعات';
      }
      return 'مرتجعات';
    } else if (transactionType === 'income') {
      return 'إيداع نقدي';
    } else if (transactionType === 'expense') {
      return 'سحب نقدي';
    } else if (transactionType === 'fix') {
      return 'تصحيح الخزينة';
    } else if (transactionType === 'delete-income') {
      return 'حذف إيداع';
    } else if (transactionType === 'delete-expense') {
      return 'حذف مصروف';
    }

    // النص الافتراضي
    return transactionType || 'غير معروف';
  };

  return (
    <div className="cashbox-page">
      <div className="cashbox-header">
        <h1>الخزينة</h1>
        <p>إدارة الخزينة والمعاملات المالية</p>
      </div>

      {loading && <div className="loading">جاري التحميل...</div>}

      {error && <div className="error">{error}</div>}

      {!loading && !error && (
        <>
          {/* عرض معلومات الخزينة */}
          <div className="cashbox-info">
            {cashbox && cashbox.exists ? (
              <div className="cashbox-cards">
                <Card
                  title="الرصيد الافتتاحي"
                  icon={<FaWallet />}
                  className="cashbox-card"
                  actions={
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        setInitialBalance(cashbox.initial_balance);
                        setShowEditInitialBalanceModal(true);
                        // إعادة تمكين حقول النموذج
                        setTimeout(enableFormFields, 100);
                      }}
                    >
                      تعديل
                    </Button>
                  }
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.initial_balance} />
                  </div>
                </Card>

                <Card title="الرصيد الحالي" icon={<FaWallet />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.current_balance} />
                  </div>
                </Card>

                <Card title="إجمالي المبيعات" icon={<FaArrowUp />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.sales_total || 0} />
                  </div>
                </Card>

                <Card title="إجمالي المشتريات" icon={<FaArrowDown />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.purchases_total || 0} />
                  </div>
                </Card>

                <Card title="إجمالي المرتجعات" icon={<FaExchangeAlt />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.returns_total || 0} />
                  </div>
                </Card>

                <Card title="إجمالي مصاريف النقل" icon={<FaTruck />} className="cashbox-card">
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.transport_total || 0} />
                  </div>
                </Card>

                <Card
                  title="إجمالي الأرباح"
                  icon={<FaChartLine />}
                  className="cashbox-card profit-card"
                  actions={
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={handleRecalculateProfits}
                      disabled={loading}
                    >
                      إعادة حساب
                    </Button>
                  }
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.profit_total || 0} />
                  </div>
                </Card>
              </div>
            ) : (
              <div className="no-cashbox">
                <p>يمكنك إضافة معاملة مباشرة لإنشاء خزينة تلقائياً.</p>
                <div className="cashbox-actions">
                  <Button
                    variant="primary"
                    icon={<FaPlus />}
                    onClick={() => {
                      setShowAddTransactionModal(true);
                      setTimeout(enableFormFields, 100);
                    }}
                    disabled={loading}
                    style={{ marginRight: '10px' }}
                  >
                    إضافة معاملة
                  </Button>
                  <Button
                    variant="secondary"
                    icon={<FaPlus />}
                    onClick={() => {
                      setShowInitialBalanceModal(true);
                      setTimeout(enableFormFields, 100);
                    }}
                    disabled={loading}
                  >
                    إنشاء خزينة جديدة
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* عرض المعاملات */}
          <Card
            title="المعاملات المالية"
            icon={<FaMoneyBillWave />}
            actions={
              <>
                <Button
                  variant="secondary"
                  icon={<FaFilter />}
                  onClick={() => setShowFilters(!showFilters)}
                >
                  تصفية متقدمة
                </Button>
                <Button
                  variant="primary"
                  icon={<FaPlus />}
                  onClick={() => {
                    setShowAddTransactionModal(true);
                    setTimeout(enableFormFields, 100);
                  }}
                  disabled={loading}
                >
                  إضافة معاملة
                </Button>
              </>
            }
          >
            {/* تم إزالة أزرار التصفية السريعة بناءً على طلب المستخدم */}
              {/* نموذج التصفية */}
              {showFilters && (
                <div className="filter-form">
                  <div className="filter-row">
                    <div className="filter-group">
                      <label>نوع المعاملة</label>
                      <select
                        value={filters.type}
                        onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                      >
                        <option value="">الكل</option>
                        <option value="income">دخل</option>
                        <option value="expense">مصروفات</option>
                        <option value="return">مرتجعات</option>
                      </select>
                    </div>

                    <div className="filter-group">
                      <label>المصدر</label>
                      <input
                        type="text"
                        value={filters.source}
                        onChange={(e) => setFilters({ ...filters, source: e.target.value })}
                        placeholder="بحث في المصدر"
                      />
                    </div>
                  </div>

                  <div className="filter-row">
                    <div className="filter-group">
                      <label>من تاريخ</label>
                      <input
                        type="date"
                        value={filters.startDate}
                        onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                      />
                    </div>

                    <div className="filter-group">
                      <label>إلى تاريخ</label>
                      <input
                        type="date"
                        value={filters.endDate}
                        onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="filter-actions">
                    <Button variant="secondary" onClick={resetFilters}>إعادة تعيين</Button>
                    <Button variant="primary" onClick={applyFilters}>تطبيق</Button>
                  </div>
                </div>
              )}

              {/* جدول المعاملات */}
              <div className="transactions-table-container">
                {transactions.length === 0 ? (
                  <div className="no-transactions">
                    <p>لا توجد معاملات مسجلة بعد.</p>
                  </div>
                ) : (
                  <table className="transactions-table">
                    <thead>
                      <tr>
                        <th>#</th>
                        <th>النوع</th>
                        <th>المبلغ</th>
                        <th>المصدر</th>
                        <th>التاريخ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((transaction, index) => (
                        <tr key={`${transaction.id}-${index}`}>
                          <td>{index + 1}</td>
                          <td>
                            <div className="transaction-type">
                              {getTransactionIcon(transaction.type, transaction.source)}
                              <span>{getTransactionTypeText(transaction.type, transaction.source)}</span>
                              {transaction.items_count > 1 && (
                                <span className="transaction-items-count">
                                  ({transaction.items_count} صنف)
                                </span>
                              )}
                            </div>
                          </td>
                          <td className={getAmountColor(transaction.type, transaction.source)}>
                            <FormattedCurrency amount={transaction.amount} />
                          </td>
                          <td>
                            {/* استخدام نفس الدالة getTransactionTypeText للحصول على نص متسق */}
                            {getTransactionTypeText(transaction.type, transaction.source)}
                          </td>
                          <td>{formatDate(transaction.created_at)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </Card>
        </>
      )}

      {/* نافذة إنشاء خزينة جديدة */}
      <Modal
        isOpen={showInitialBalanceModal}
        onClose={() => setShowInitialBalanceModal(false)}
        title="إنشاء خزينة جديدة"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>الرصيد الافتتاحي</label>
            <input
              type="number"
              value={initialBalance}
              onChange={(e) => setInitialBalance(e.target.value)}
              placeholder="أدخل الرصيد الافتتاحي"
              min="0"
              step="0.01"
            />
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowInitialBalanceModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateCashbox}
              disabled={loading}
            >
              إنشاء
            </Button>
          </div>
        </div>
      </Modal>

      {/* نافذة تعديل الرصيد الافتتاحي */}
      <Modal
        isOpen={showEditInitialBalanceModal}
        onClose={() => setShowEditInitialBalanceModal(false)}
        title="تعديل الرصيد الافتتاحي"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>الرصيد الافتتاحي الجديد</label>
            <input
              type="number"
              value={initialBalance}
              onChange={(e) => setInitialBalance(e.target.value)}
              placeholder="أدخل الرصيد الافتتاحي الجديد"
              min="0"
              step="0.01"
            />
            <p className="form-hint">
              ملاحظة: تعديل الرصيد الافتتاحي قد يؤثر على الرصيد الحالي.
            </p>
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowEditInitialBalanceModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={async () => {
                if (!initialBalance || isNaN(initialBalance) || initialBalance < 0) {
                  showNotification('الرجاء إدخال رصيد افتتاحي صحيح', 'error');
                  return;
                }

                try {
                  setLoading(true);
                  await updateCashboxInitialBalance(Number(initialBalance));

                  if (!isMounted.current) return;

                  showNotification('تم تحديث الرصيد الافتتاحي بنجاح', 'success');
                  setShowEditInitialBalanceModal(false);
                  await loadCashbox();
                } catch (error) {
                  console.error('Error updating initial balance:', error);
                  if (isMounted.current) {
                    showNotification('حدث خطأ أثناء تحديث الرصيد الافتتاحي', 'error');
                  }
                } finally {
                  if (isMounted.current) {
                    setLoading(false);
                  }
                }
              }}
              disabled={loading}
            >
              حفظ التغييرات
            </Button>
          </div>
        </div>
      </Modal>

      {/* نافذة إضافة معاملة جديدة */}
      <Modal
        isOpen={showAddTransactionModal}
        onClose={() => setShowAddTransactionModal(false)}
        title="إضافة معاملة جديدة"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>نوع المعاملة</label>
            <div className="transaction-type-buttons">
              <button
                className={`type-button ${transactionForm.type === 'income' ? 'active' : ''}`}
                onClick={() => setTransactionForm({ ...transactionForm, type: 'income' })}
              >
                <FaArrowUp /> دخل
              </button>
              <button
                className={`type-button ${transactionForm.type === 'expense' ? 'active' : ''}`}
                onClick={() => setTransactionForm({ ...transactionForm, type: 'expense' })}
              >
                <FaArrowDown /> مصروفات
              </button>
            </div>
          </div>

          <div className="form-group">
            <label>المبلغ</label>
            <input
              type="number"
              value={transactionForm.amount}
              onChange={(e) => setTransactionForm({ ...transactionForm, amount: e.target.value })}
              placeholder="أدخل المبلغ"
              min="0.01"
              step="0.01"
            />
            {transactionForm.type === 'income' && cashbox && (
              <>
                <p className="form-hint">
                  الرصيد الحالي: {cashbox.current_balance} | الرصيد الافتتاحي: {cashbox.initial_balance}
                </p>
              </>
            )}
          </div>

          <div className="form-group">
            <label>المصدر</label>
            <input
              type="text"
              value={transactionForm.source}
              onChange={(e) => setTransactionForm({ ...transactionForm, source: e.target.value })}
              placeholder="أدخل مصدر المعاملة"
            />
          </div>

          <div className="form-group">
            <label>ملاحظات (اختياري)</label>
            <textarea
              value={transactionForm.notes}
              onChange={(e) => setTransactionForm({ ...transactionForm, notes: e.target.value })}
              placeholder="أدخل ملاحظات إضافية"
              rows="3"
            />
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowAddTransactionModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleAddTransaction}
              disabled={loading}
            >
              إضافة
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Cashbox;
